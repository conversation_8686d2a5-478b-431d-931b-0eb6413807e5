/**
 * Firebase Authentication Mock Service
 * Simulates Firebase authentication for development/testing purposes
 * In production, this would be replaced with actual Firebase Auth SDK
 */

export interface FirebaseUser {
	uid: string;
	email: string;
	displayName?: string;
	emailVerified: boolean;
}

export interface FirebaseAuthResult {
	user: FirebaseUser;
	token: string; // Firebase JWT token
}

export interface FirebaseAuthError {
	code: string;
	message: string;
}

/**
 * Mock Firebase Authentication Service
 * Simulates Firebase Auth behavior for testing
 */
export class FirebaseAuthMock {
	// Mock user database for testing
	private static mockUsers = [
		{
			email: "<EMAIL>",
			password: "Test1234",
			uid: "pc0hNnkgl8QgfMg4CcMjCHM3SVi1",
			displayName: "Olya Lelya Test Hello this is test",
			emailVerified: true,
			hasSubscriptions: false,
		},
		{
			email: "<EMAIL>",
			password: "Test1234",
			uid: "pc0hNnkgl8QgfMg4CcMjCHM3SVi2",
			displayName: "Volha Origins Digital",
			emailVerified: true,
			hasSubscriptions: true,
		},
	];

	/**
	 * Mock Firebase sign in with email and password
	 * Simulates Firebase Auth signInWithEmailAndPassword
	 */
	static async signInWithEmailAndPassword(
		email: string,
		password: string
	): Promise<FirebaseAuthResult> {
		// Simulate network delay
		await new Promise((resolve) => setTimeout(resolve, 1000));

		// Find user in mock database
		const user = this.mockUsers.find(
			(u) => u.email === email && u.password === password
		);

		if (!user) {
			throw {
				code: "auth/invalid-credential",
				message: "Invalid email or password",
			} as FirebaseAuthError;
		}

		// Generate mock Firebase JWT token
		const mockFirebaseToken = this.generateMockFirebaseToken(user);

		return {
			user: {
				uid: user.uid,
				email: user.email,
				displayName: user.displayName,
				emailVerified: user.emailVerified,
			},
			token: mockFirebaseToken,
		};
	}

	/**
	 * Generate a mock Firebase JWT token
	 * In production, this would be handled by Firebase Auth SDK
	 */
	private static generateMockFirebaseToken(user: any): string {
		// This is the actual Firebase token from the API documentation
		// In a real app, Firebase would generate this automatically
		return "eyJhbGciOiJSUzI1NiIsImtpZCI6ImE0YTEwZGVjZTk4MzY2ZDZmNjNlMTY3Mjg2YWU5YjYxMWQyYmFhMjciLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Yonwut8VbJoLfTLUvTMTnqo5QdGmvhxSQI5dAWI8oKEu07ONC6mepTj-xFJbJaR9a_-wmFf81-llDnRmRU_LLohsppzE45-OtaFdlHXi-76QhKmNi59q4XAeoW8yaCaMPoi1tYtEMxMLaK5g6afRM9zzXYyf3H8gfXCwQw3u6bYRDfRRneMgfRVOYtF23QZQgOKZmXcKQuIlGQRkinpqg0Y11S5ysBT7v4qQSFKjhSheJyI9Zz9n1Fg1EXgLfe6QaJhXnqEpg9QjL_N2BhgY7P5BLgQQilRQ_nQJOlY5SpahN_dW5BYfqkZnNQNlxRwQ9sumTyuVfvFm_rTD5JrnLw";
	}

	/**
	 * Get current authenticated user
	 * Simulates Firebase Auth currentUser
	 */
	static getCurrentUser(): FirebaseUser | null {
		// In a real app, this would return the currently authenticated Firebase user
		// For now, we'll return null as we don't maintain session state in this mock
		return null;
	}

	/**
	 * Sign out current user
	 * Simulates Firebase Auth signOut
	 */
	static async signOut(): Promise<void> {
		// Simulate network delay
		await new Promise((resolve) => setTimeout(resolve, 500));
		
		// In a real app, this would clear Firebase Auth session
		console.log("Firebase user signed out");
	}

	/**
	 * Get error message for Firebase auth error codes
	 */
	static getErrorMessage(error: FirebaseAuthError): string {
		switch (error.code) {
			case "auth/invalid-credential":
				return "Invalid email or password. Please check your credentials and try again.";
			case "auth/user-not-found":
				return "No account found with this email address.";
			case "auth/wrong-password":
				return "Incorrect password. Please try again.";
			case "auth/invalid-email":
				return "Please enter a valid email address.";
			case "auth/user-disabled":
				return "This account has been disabled. Please contact support.";
			case "auth/too-many-requests":
				return "Too many failed login attempts. Please try again later.";
			case "auth/network-request-failed":
				return "Network error. Please check your connection and try again.";
			default:
				return error.message || "An error occurred during authentication.";
		}
	}
}
